{"common": {"welcome": "Welcome 👋", "letsGetStarted": "Let's get you started.", "createProject": "Create Project", "invitePeople": "Invite People", "recentProjects": "Recent Projects", "viewAll": "View All", "resources": "Resources", "learnExploreGetHelp": "Learn, explore and get help", "documentation": "Documentation", "apiDocumentation": "API Documentation", "releaseNotes": "Release Notes", "blog": "LabelStud.io Blog", "slackCommunity": "Slack Community", "labelStudioVersion": "Label Studio Version: Community", "cantLoadProjects": "can't load projects", "createFirstProject": "Create your first project", "importDataSetupInterface": "Import your data and set up the labeling interface to start annotating", "projects": "Projects", "create": "Create", "loading": "Loading...", "home": "Home", "settings": "Settings", "tasks": "Tasks", "progress": "Progress", "finished": "finished", "of": "of", "percent": "%", "delete": "Delete", "save": "Save", "saved": "Saved!", "learnMore": "Learn more"}, "navigation": {"home": "Home", "projects": "Projects", "settings": "Settings", "organization": "Organization", "dataManager": "Data Manager", "export": "Export", "webhooks": "Webhooks"}, "language": {"english": "English", "chinese": "中文", "switchLanguage": "Switch Language"}, "createProject": {"title": "Create Project", "projectName": "Project Name", "description": "Description", "descriptionPlaceholder": "Optional description of your project", "workspace": "Workspace", "selectOption": "Select an option", "workspaceDescription": "Simplify project management by organizing projects into workspaces.", "dataImport": "Data Import", "labelingSetup": "Labeling Setup"}, "export": {"title": "Export data", "export": "Export", "preparingMessage": "Files are being prepared. It might take some time.", "formatInfo": "You can export dataset in one of the following formats:", "cantFindFormat": "Can't find an export format?", "letUsKnow": "Please let us know in", "orSubmitIssue": "or submit an issue to the", "repository": "Repository"}, "settings": {"generalSettings": "General Settings", "color": "Color", "taskSampling": "Task Sampling", "sequential": "Sequential", "sequentialDescription": "Tasks are ordered by Task ID", "random": "Random", "randomDescription": "Tasks are chosen with uniform random", "uncertaintySampling": "Uncertainty sampling", "uncertaintySamplingDescription": "Tasks are chosen according to model uncertainty score (active learning mode)."}}